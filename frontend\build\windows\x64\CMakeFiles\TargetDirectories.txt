E:/github/local_upload/frontend/build/windows/x64/CMakeFiles/INSTALL.dir
E:/github/local_upload/frontend/build/windows/x64/CMakeFiles/ALL_BUILD.dir
E:/github/local_upload/frontend/build/windows/x64/CMakeFiles/ZERO_CHECK.dir
E:/github/local_upload/frontend/build/windows/x64/flutter/CMakeFiles/flutter_wrapper_plugin.dir
E:/github/local_upload/frontend/build/windows/x64/flutter/CMakeFiles/flutter_wrapper_app.dir
E:/github/local_upload/frontend/build/windows/x64/flutter/CMakeFiles/flutter_assemble.dir
E:/github/local_upload/frontend/build/windows/x64/flutter/CMakeFiles/INSTALL.dir
E:/github/local_upload/frontend/build/windows/x64/runner/CMakeFiles/telegram_uploader.dir
E:/github/local_upload/frontend/build/windows/x64/runner/CMakeFiles/INSTALL.dir
E:/github/local_upload/frontend/build/windows/x64/runner/CMakeFiles/ALL_BUILD.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/screen_retriever/CMakeFiles/screen_retriever_plugin.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/screen_retriever/CMakeFiles/INSTALL.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/screen_retriever/CMakeFiles/ALL_BUILD.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/system_tray/CMakeFiles/system_tray_plugin.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/system_tray/CMakeFiles/INSTALL.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/system_tray/CMakeFiles/ALL_BUILD.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/window_manager/CMakeFiles/window_manager_plugin.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/window_manager/CMakeFiles/INSTALL.dir
E:/github/local_upload/frontend/build/windows/x64/plugins/window_manager/CMakeFiles/ALL_BUILD.dir
