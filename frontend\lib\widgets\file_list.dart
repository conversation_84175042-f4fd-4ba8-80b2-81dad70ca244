import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';

class FileList extends StatelessWidget {
  const FileList({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '文件列表',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            
            if (appProvider.scannedFiles.isEmpty) ...[
              Expanded(
                child: Center(
                  child: Text(
                    '请选择文件夹扫描音视频文件',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ] else ...[
              // 文件统计
              Text(
                '找到 ${appProvider.scannedFiles.length} 个文件',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              
              // 文件列表
              Expanded(
                child: ListView.builder(
                  itemCount: appProvider.scannedFiles.length,
                  itemBuilder: (context, index) {
                    final file = appProvider.scannedFiles[index];
                    return ListTile(
                      leading: Icon(
                        file.type == 'video' ? Icons.video_file : Icons.audio_file,
                        color: file.type == 'video' ? Colors.blue : Colors.green,
                      ),
                      title: Text(
                        file.name,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: Text(
                        '${file.sizeFormatted} • ${file.caption}',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      trailing: Chip(
                        label: Text(
                          file.type == 'video' ? '视频' : '音频',
                          style: const TextStyle(fontSize: 12),
                        ),
                        backgroundColor: file.type == 'video' 
                          ? Colors.blue.shade100 
                          : Colors.green.shade100,
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        );
      },
    );
  }
} 