import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../providers/app_provider.dart';
import '../providers/account_provider.dart';
import '../providers/task_provider.dart';
import '../models/account.dart';
import '../models/file_info.dart';
import 'custom_text_field.dart';

class UploadForm extends StatefulWidget {
  const UploadForm({super.key});

  @override
  State<UploadForm> createState() => _UploadFormState();
}

class _UploadFormState extends State<UploadForm> {
  Account? _selectedAccount;
  String _selectedFolderPath = '';
  String _selectedGroupId = '';
  String _selectedGroupName = '';
  
  final _folderController = TextEditingController();
  final _groupController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    
    // 监听账号变化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);
      if (accountProvider.selectedAccount != null) {
        setState(() {
          _selectedAccount = accountProvider.selectedAccount;
        });
      }
    });
  }
  
  @override
  void dispose() {
    _folderController.dispose();
    _groupController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer3<AppProvider, AccountProvider, TaskProvider>(
      builder: (context, appProvider, accountProvider, taskProvider, child) {
        // 同步选中的账号
        if (accountProvider.selectedAccount != null && 
            _selectedAccount != accountProvider.selectedAccount) {
          // 确保选中的账号在已登录账号列表中
          if (accountProvider.loggedInAccounts.contains(accountProvider.selectedAccount)) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _selectedAccount = accountProvider.selectedAccount;
              });
            });
          }
        }
        
        // 如果当前选中的账号不在已登录列表中，清除选择
        if (_selectedAccount != null && 
            !accountProvider.loggedInAccounts.contains(_selectedAccount)) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _selectedAccount = null;
            });
          });
        }
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  '上传配置',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                
                // 账号选择
                _buildAccountSelector(accountProvider),
                const SizedBox(height: 16),
                
                // 文件夹选择
                _buildFolderSelector(appProvider),
                const SizedBox(height: 16),
                
                // 群组选择
                _buildGroupSelector(accountProvider),
                const SizedBox(height: 16),
                
                // 文件夹统计
                if (appProvider.folderStats != null)
                  _buildFolderStats(appProvider.folderStats!),
                const SizedBox(height: 16),
                
                // 上传按钮
                _buildUploadButton(taskProvider),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildAccountSelector(AccountProvider accountProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择账号',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        
        if (accountProvider.accounts.isEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              '请先在设置中添加账号',
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ] else ...[
          DropdownButtonFormField<Account>(
            value: _selectedAccount,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '选择账号',
            ),
            items: accountProvider.loggedInAccounts.map((account) {
              return DropdownMenuItem(
                value: account,
                child: Row(
                  children: [
                    Icon(
                      Icons.account_circle,
                      color: accountProvider.isAccountLoggedIn(account.id) 
                        ? Colors.green 
                        : Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Text(account.name),
                  ],
                ),
              );
            }).toList(),
            onChanged: (Account? account) {
              setState(() {
                _selectedAccount = account;
              });
              
              if (account != null) {
                accountProvider.setSelectedAccount(account);
              }
            },
          ),
          
          if (accountProvider.loggedInAccounts.isEmpty)
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                '没有已登录的账号，请先登录',
                style: TextStyle(color: Colors.orange),
              ),
            ),
        ],
      ],
    );
  }
  
  Widget _buildFolderSelector(AppProvider appProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择文件夹',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _folderController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: '选择包含音视频文件的文件夹',
                ),
                readOnly: true,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: _selectFolder,
              icon: const Icon(Icons.folder),
              label: const Text('浏览'),
            ),
          ],
        ),
        
        // 文件夹历史
        if (appProvider.folderHistory.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            '历史文件夹:',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: 4),
          Container(
            height: 100,
            child: ListView.builder(
              itemCount: appProvider.folderHistory.length,
              itemBuilder: (context, index) {
                final folder = appProvider.folderHistory[index];
                return ListTile(
                  dense: true,
                  title: Text(
                    folder,
                    style: Theme.of(context).textTheme.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: const Icon(Icons.history, size: 16),
                  onTap: () {
                    _setSelectedFolder(folder);
                  },
                );
              },
            ),
          ),
        ],
      ],
    );
  }
  
  Widget _buildGroupSelector(AccountProvider accountProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择目标群组/频道',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        
        if (_selectedAccount == null || 
            !accountProvider.isAccountLoggedIn(_selectedAccount!.id)) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              '请先选择并登录账号',
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ] else ...[
          // 如果是机器人账号，显示特殊提示
          if (_selectedAccount!.accountType == 'bot') ...[
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border.all(color: Colors.blue.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Bot账号需要手动输入聊天ID（群组ID、频道ID或用户名）',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 13,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          CustomTextFormField(
            controller: _groupController,
            hintText: _selectedAccount!.accountType == 'bot'
                ? '输入聊天ID，例如: -************* 或 @username'
                : '输入群组ID或选择群组',
            suffixIcon: IconButton(
              icon: const Icon(Icons.search),
              onPressed: _showGroupSelector,
              tooltip: _selectedAccount!.accountType == 'bot'
                  ? '打开聊天ID输入界面'
                  : '选择群组',
            ),
            enableSuggestions: true,
            autocorrect: false,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.done,
            onChanged: (value) {
              setState(() {
                _selectedGroupId = value;
                _selectedGroupName = '';
              });
            },
          ),
          
          // 群组历史（用户账号显示）
          if (_selectedAccount!.accountType != 'bot' && 
              accountProvider.groupHistory.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              '历史群组:',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 4),
            Container(
              height: 100,
              child: ListView.builder(
                itemCount: accountProvider.groupHistory.length,
                itemBuilder: (context, index) {
                  final group = accountProvider.groupHistory[index];
                  return ListTile(
                    dense: true,
                    title: Text(
                      group.title,
                      style: Theme.of(context).textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                    subtitle: Text(
                      group.id,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                    trailing: Icon(_getDialogIcon(group.type), size: 16),
                    onTap: () {
                      _setSelectedGroup(group.id, group.title);
                    },
                  );
                },
              ),
            ),
          ],
        ],
      ],
    );
  }
  
  Widget _buildFolderStats(FolderStats stats) {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '文件夹统计',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            
            // 如果没有找到音视频文件，显示调试信息
            if (stats.totalFiles == 0) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.orange),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '未找到支持的文件',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    if (stats.totalFilesInFolder != null)
                      Text('文件夹中总文件数: ${stats.totalFilesInFolder}'),
                    const SizedBox(height: 4),
                    Text(
                      '支持的格式：',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '视频: .mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v, .3gp, .mpg, .mpeg, .ts, .vob, .rm, .rmvb, .asf',
                      style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                    ),
                    Text(
                      '音频: .mp3, .wav, .flac, .aac, .ogg, .wma, .m4a, .opus, .aiff, .ape, .dts, .ac3, .amr',
                      style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                    ),
                    Text(
                      '图片: .jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp, .svg, .ico, .heic',
                      style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                    ),
                    Text(
                      '文档: .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt, .rtf, .odt, .ods, .odp, .csv',
                      style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                    ),
                    Text(
                      '压缩: .zip, .rar, .7z, .tar, .gz, .bz2, .xz',
                      style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                    ),
                    Text(
                      '其他格式也支持上传',
                      style: TextStyle(fontSize: 12, color: Colors.grey[700], fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ] else ...[
              // 正常统计信息显示
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 第一行：总文件数和总大小
                  Row(
                    children: [
                      Expanded(
                        child: Text('总文件数: ${stats.totalFiles}', 
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      ),
                      Expanded(
                        child: Text('总大小: ${stats.totalSizeFormatted}', 
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // 文件类型统计网格
                  Row(
                    children: [
                      if (stats.videoCount > 0)
                        Expanded(child: _buildFileTypeChip('视频', stats.videoCount, Colors.blue)),
                      if (stats.audioCount > 0)
                        Expanded(child: _buildFileTypeChip('音频', stats.audioCount, Colors.green)),
                      if ((stats.imageCount ?? 0) > 0)
                        Expanded(child: _buildFileTypeChip('图片', stats.imageCount!, Colors.purple)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      if ((stats.documentCount ?? 0) > 0)
                        Expanded(child: _buildFileTypeChip('文档', stats.documentCount!, Colors.orange)),
                      if ((stats.archiveCount ?? 0) > 0)
                        Expanded(child: _buildFileTypeChip('压缩', stats.archiveCount!, Colors.brown)),
                      if ((stats.otherCount ?? 0) > 0)
                        Expanded(child: _buildFileTypeChip('其他', stats.otherCount!, Colors.grey)),
                    ],
                  ),
                  
                  // 额外信息
                  if (stats.totalFilesInFolder != null && stats.totalFilesInFolder! > stats.totalFiles) ...[
                    const SizedBox(height: 8),
                    Text(
                      '文件夹总文件: ${stats.totalFilesInFolder} (跳过了 ${stats.totalFilesInFolder! - stats.totalFiles} 个文件)',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                  
                  if (stats.totalFiles > 0) ...[
                    const SizedBox(height: 4),
                    Text(
                      '平均大小: ${(stats.totalSize / stats.totalFiles / 1024 / 1024).toStringAsFixed(1)} MB',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildUploadButton(TaskProvider taskProvider) {
    final canUpload = _selectedAccount != null && 
                     _selectedFolderPath.isNotEmpty && 
                     _selectedGroupId.isNotEmpty;
    
    return ElevatedButton.icon(
      onPressed: canUpload && !taskProvider.isLoading 
        ? _startUpload 
        : null,
      icon: taskProvider.isLoading 
        ? const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : const Icon(Icons.upload),
      label: Text(taskProvider.isLoading ? '上传中...' : '开始上传'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        backgroundColor: canUpload ? Colors.blue : null,
        foregroundColor: canUpload ? Colors.white : null,
      ),
    );
  }
  
  Future<void> _selectFolder() async {
    final result = await FilePicker.platform.getDirectoryPath();
    
    if (result != null) {
      _setSelectedFolder(result);
    }
  }
  
  void _setSelectedFolder(String folderPath) {
    setState(() {
      _selectedFolderPath = folderPath;
      _folderController.text = folderPath;
    });
    
    // 扫描文件夹
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    appProvider.scanFolder(folderPath);
  }
  
  void _setSelectedGroup(String groupId, String groupName) {
    setState(() {
      _selectedGroupId = groupId;
      _selectedGroupName = groupName;
      _groupController.text = groupName.isNotEmpty ? groupName : groupId;
    });
  }
  
  Future<void> _showGroupSelector() async {
    final accountProvider = Provider.of<AccountProvider>(context, listen: false);
    
    if (_selectedAccount == null || 
        !accountProvider.isAccountLoggedIn(_selectedAccount!.id)) {
      _showError('请先选择并登录账号');
      return;
    }
    
    // 刷新对话列表
    await accountProvider.refreshDialogs();
    
    if (!mounted) return;
    
    // 检查是否是机器人账号
    final isBotAccount = _selectedAccount!.accountType == 'bot';
    
    final selectedGroup = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) {
        return _GroupSelectorDialog(
          isBotAccount: isBotAccount,
          accountProvider: accountProvider,
        );
      },
    );
    
    if (selectedGroup != null) {
      _setSelectedGroup(selectedGroup['id']!, selectedGroup['name']!);
    }
  }
  
  IconData _getDialogIcon(String type) {
    switch (type) {
      case 'channel':
        return Icons.campaign;
      case 'group':
      case 'supergroup':
        return Icons.group;
      default:
        return Icons.person;
    }
  }
  
  Widget _buildFileTypeChip(String label, int count, Color color) {
    return Chip(
      label: Text('$label: $count'),
      backgroundColor: color.withOpacity(0.2),
      labelStyle: TextStyle(color: color, fontWeight: FontWeight.bold),
    );
  }
  
  Future<void> _startUpload() async {
    final taskProvider = Provider.of<TaskProvider>(context, listen: false);
    
    final success = await taskProvider.startUpload(
      accountId: _selectedAccount!.id,
      folderPath: _selectedFolderPath,
      targetChatId: _selectedGroupId,
      targetChatName: _selectedGroupName,
    );
    
    if (success) {
      _showSuccess('上传任务已启动');
    } else {
      _showError(taskProvider.error ?? '启动上传失败');
    }
  }
  
  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}

class _GroupSelectorDialog extends StatefulWidget {
  final bool isBotAccount;
  final AccountProvider accountProvider;

  const _GroupSelectorDialog({
    required this.isBotAccount,
    required this.accountProvider,
  });

  @override
  State<_GroupSelectorDialog> createState() => _GroupSelectorDialogState();
}

class _GroupSelectorDialogState extends State<_GroupSelectorDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _manualChatId = '';

  @override
  void initState() {
    super.initState();
    // 机器人账号只有一个tab（输入历史），用户账号有两个tab（搜索和历史）
    _tabController = TabController(
      length: widget.isBotAccount ? 1 : 2,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.isBotAccount ? '输入聊天ID' : '选择群组/频道'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            // Tab栏
            if (!widget.isBotAccount) ...[
              TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: '搜索'),
                  Tab(text: '历史'),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // Tab内容
            Expanded(
              child: widget.isBotAccount
                  ? _buildBotInputTab()
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildSearchTab(),
                        _buildHistoryTab(),
                      ],
                    ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        if (widget.isBotAccount)
          ElevatedButton(
            onPressed: _manualChatId.isNotEmpty
                ? () {
                    Navigator.of(context).pop({
                      'id': _manualChatId,
                      'name': _manualChatId,
                    });
                  }
                : null,
            child: const Text('确定'),
          ),
      ],
    );
  }

  Widget _buildBotInputTab() {
    return Column(
      children: [
        const Text(
          '机器人账号无法获取对话列表，请手动输入聊天ID：',
          style: TextStyle(fontSize: 14, color: Colors.grey),
        ),
        const SizedBox(height: 12),
        CustomTextFormField(
          labelText: '聊天ID',
          hintText: '例如: -************* 或 @username',
          enableSuggestions: true,
          autocorrect: false,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.done,
          onChanged: (value) {
            setState(() {
              _manualChatId = value;
            });
          },
        ),
        const SizedBox(height: 16),

        // 输入历史
        if (widget.accountProvider.botInputHistory.isNotEmpty) ...[
          const Divider(),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '输入历史:',
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: widget.accountProvider.botInputHistory.length,
              itemBuilder: (context, index) {
                final input = widget.accountProvider.botInputHistory[index];
                return ListTile(
                  title: Text(input.title),
                  subtitle: Text('聊天ID: ${input.id}'),
                  trailing: const Icon(Icons.history),
                  onTap: () {
                    Navigator.of(context).pop({
                      'id': input.id,
                      'name': input.title,
                    });
                  },
                );
              },
            ),
          ),
        ] else ...[
          const SizedBox(height: 16),
          const Expanded(
            child: Center(
              child: Text(
                '暂无输入历史',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSearchTab() {
    return widget.accountProvider.dialogs.isEmpty
        ? const Center(
            child: Text('暂无对话数据'),
          )
        : ListView.builder(
            itemCount: widget.accountProvider.dialogs.length,
            itemBuilder: (context, index) {
              final dialog = widget.accountProvider.dialogs[index];

              // 如果是信息提示项
              if (dialog.isInfo == true) {
                return Card(
                  color: Colors.blue.shade50,
                  child: ListTile(
                    leading: const Icon(Icons.info, color: Colors.blue),
                    title: Text(
                      dialog.title,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: dialog.description != null
                        ? Text(dialog.description!)
                        : null,
                    // 信息项不可点击
                    onTap: null,
                  ),
                );
              }

              // 普通对话项
              return ListTile(
                title: Text(dialog.title),
                subtitle: Text('${dialog.type} • ${dialog.id}'),
                trailing: Icon(_getDialogIcon(dialog.type)),
                onTap: () {
                  Navigator.of(context).pop({
                    'id': dialog.id,
                    'name': dialog.title,
                  });
                },
              );
            },
          );
  }

  Widget _buildHistoryTab() {
    return widget.accountProvider.groupHistory.isEmpty
        ? const Center(
            child: Text('暂无历史记录'),
          )
        : ListView.builder(
            itemCount: widget.accountProvider.groupHistory.length,
            itemBuilder: (context, index) {
              final group = widget.accountProvider.groupHistory[index];
              return ListTile(
                title: Text(group.title),
                subtitle: Text('历史记录 • ${group.id}'),
                trailing: const Icon(Icons.history),
                onTap: () {
                  Navigator.of(context).pop({
                    'id': group.id,
                    'name': group.title,
                  });
                },
              );
            },
          );
  }

  IconData _getDialogIcon(String type) {
    switch (type) {
      case 'channel':
        return Icons.campaign;
      case 'group':
      case 'supergroup':
        return Icons.group;
      default:
        return Icons.person;
    }
  }
}