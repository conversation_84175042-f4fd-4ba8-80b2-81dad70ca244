#  Pyrogram - <PERSON><PERSON>ram MTProto API Client Library for Python
#  Copyright (C) 2017-present Dan <https://github.com/delivrance>
#
#  This file is part of Pyrogram.
#
#  Pyrogram is free software: you can redistribute it and/or modify
#  it under the terms of the GNU Lesser General Public License as published
#  by the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  Pyrogram is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU Lesser General Public License for more details.
#
#  You should have received a copy of the GNU Lesser General Public License
#  along with Pyrogram.  If not, see <http://www.gnu.org/licenses/>.

import pyrogram
from pyrogram import raw


class ToggleFolderTags:
    async def toggle_folder_tags(
        self: "pyrogram.Client",
        are_tags_enabled: bool
    ) -> bool:
        """Toggles whether chat folder tags are enabled.

        .. include:: /_includes/usable-by/users.rst

        Parameters:
            are_tags_enabled (``bool``):
                Pass True to enable folder tags.
                Pass False to disable them.

        Returns:
            ``bool``: On success, True is returned.

        Example:
            .. code-block:: python

                await app.toggle_folder_tags(True)
        """
        r = await self.invoke(
            raw.functions.messages.ToggleDialogFilterTags(
                enabled=are_tags_enabled
            )
        )

        return r
