// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Account _$AccountFromJson(Map<String, dynamic> json) => Account(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      apiId: (json['api_id'] as num).toInt(),
      apiHash: json['api_hash'] as String,
      phone: json['phone'] as String?,
      sessionString: json['session_string'] as String?,
      accountType: json['account_type'] as String? ?? 'user',
      botToken: json['bot_token'] as String?,
      isActive: json['is_active'] as bool,
      createdAt: json['created_at'] as String,
    );

Map<String, dynamic> _$AccountToJson(Account instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'api_id': instance.apiId,
      'api_hash': instance.apiHash,
      'phone': instance.phone,
      'session_string': instance.sessionString,
      'account_type': instance.accountType,
      'bot_token': instance.botToken,
      'is_active': instance.isActive,
      'created_at': instance.createdAt,
    };

Dialog _$DialogFromJson(Map<String, dynamic> json) => Dialog(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      username: json['username'] as String?,
      isInfo: json['is_info'] as bool?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$DialogToJson(Dialog instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'type': instance.type,
      'username': instance.username,
      'is_info': instance.isInfo,
      'description': instance.description,
    };
