import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';
import 'package:system_tray/system_tray.dart';
import 'screens/main_screen.dart';
import 'providers/app_provider.dart';
import 'providers/account_provider.dart';
import 'providers/task_provider.dart';
import 'providers/proxy_provider.dart' as proxy;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 设置系统UI相关配置
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  
  // 初始化窗口管理器
  await windowManager.ensureInitialized();
  
  // 设置窗口属性
  WindowOptions windowOptions = const WindowOptions(
    size: Size(1200, 800),
    center: true,
    backgroundColor: Colors.transparent,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
    windowButtonVisibility: true,
    title: 'Telegram自动上传工具',
  );
  
  await windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });
  
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppProvider()),
        ChangeNotifierProvider(create: (_) => AccountProvider()),
        ChangeNotifierProvider(create: (_) => TaskProvider()),
        ChangeNotifierProvider(create: (_) => proxy.ProxyProvider()),
      ],
      child: MaterialApp(
        title: 'Telegram自动上传工具',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            brightness: Brightness.light,
          ),
          useMaterial3: true,
          fontFamily: 'RobotoMono',
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            brightness: Brightness.dark,
          ),
          useMaterial3: true,
          fontFamily: 'RobotoMono',
        ),
        themeMode: ThemeMode.system,
        home: const MainScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

// 系统托盘功能
class SystemTrayHelper {
  static final SystemTray _systemTray = SystemTray();
  
  static Future<void> initSystemTray() async {
    String iconPath = 'assets/icons/app_icon.ico';
    
    await _systemTray.initSystemTray(
      title: "Telegram上传工具",
      iconPath: iconPath,
    );
    
    // 设置右键菜单
    final Menu menu = Menu();
    
    await menu.buildFrom([
      MenuItemLabel(
        label: '显示主窗口',
        onClicked: (menuItem) => _showMainWindow(),
      ),
      MenuItemLabel(
        label: '暂停所有任务',
        onClicked: (menuItem) => _pauseAllTasks(),
      ),
      MenuItemLabel(
        label: '恢复所有任务',
        onClicked: (menuItem) => _resumeAllTasks(),
      ),
      MenuSeparator(),
      MenuItemLabel(
        label: '退出',
        onClicked: (menuItem) => _exitApp(),
      ),
    ]);
    
    await _systemTray.setContextMenu(menu);
    
    // 设置点击事件
    _systemTray.registerSystemTrayEventHandler((eventName) {
      if (eventName == kSystemTrayEventClick) {
        _showMainWindow();
      }
    });
  }
  
  static Future<void> _showMainWindow() async {
    await windowManager.show();
    await windowManager.focus();
  }
  
  static Future<void> _pauseAllTasks() async {
    // 暂停所有任务的逻辑
  }
  
  static Future<void> _resumeAllTasks() async {
    // 恢复所有任务的逻辑
  }
  
  static Future<void> _exitApp() async {
    await windowManager.close();
  }
  
  static Future<void> updateTooltip(String message) async {
    await _systemTray.setToolTip(message);
  }
} 