// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'proxy_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProxyConfig _$ProxyConfigFromJson(Map<String, dynamic> json) => ProxyConfig(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      enabled: json['enabled'] as bool,
      proxyType: json['proxy_type'] as String,
      hostname: json['hostname'] as String,
      port: (json['port'] as num).toInt(),
      username: json['username'] as String?,
      password: json['password'] as String?,
      isDefault: json['is_default'] as bool,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );

Map<String, dynamic> _$ProxyConfigToJson(ProxyConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'enabled': instance.enabled,
      'proxy_type': instance.proxyType,
      'hostname': instance.hostname,
      'port': instance.port,
      'username': instance.username,
      'password': instance.password,
      'is_default': instance.isDefault,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

ProxyTestResult _$ProxyTestResultFromJson(Map<String, dynamic> json) =>
    ProxyTestResult(
      success: json['success'] as bool,
      message: json['message'] as String,
      latency: (json['latency'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ProxyTestResultToJson(ProxyTestResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'latency': instance.latency,
    };
