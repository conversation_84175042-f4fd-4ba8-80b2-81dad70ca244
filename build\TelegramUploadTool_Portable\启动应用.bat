@echo off
chcp 65001 > nul
echo 启动 Telegram Upload Tool...
echo.
echo 正在检查环境...

:: 检查是否安装了Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境，请安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查是否安装了requests模块
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 未找到requests模块，正在安装...
    pip install requests
    if errorlevel 1 (
        echo ❌ 无法安装requests模块
        pause
        exit /b 1
    )
)

echo ✓ 环境检查完成
echo.
python launcher.py
pause
