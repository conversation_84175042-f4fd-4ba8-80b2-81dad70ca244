import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional

class Database:
    def __init__(self, db_path: str = "telegram_uploader.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建账号表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                api_id INTEGER NOT NULL,
                api_hash TEXT NOT NULL,
                phone TEXT,
                session_string TEXT,
                account_type TEXT DEFAULT 'user',
                bot_token TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 为现有表添加新字段（如果不存在）
        try:
            cursor.execute('ALTER TABLE accounts ADD COLUMN account_type TEXT DEFAULT "user"')
        except sqlite3.OperationalError:
            pass  # 字段已存在
        
        try:
            cursor.execute('ALTER TABLE accounts ADD COLUMN bot_token TEXT')
        except sqlite3.OperationalError:
            pass  # 字段已存在
        
        # 创建文件夹历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS folder_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                path TEXT UNIQUE NOT NULL,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建群组历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS group_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                chat_id TEXT NOT NULL,
                account_id INTEGER,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        ''')

        # 创建机器人输入历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bot_input_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chat_id TEXT NOT NULL,
                account_id INTEGER,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        ''')
        
        # 创建上传任务表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS upload_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER NOT NULL,
                folder_path TEXT NOT NULL,
                target_chat_id TEXT NOT NULL,
                target_chat_name TEXT,
                status TEXT DEFAULT 'pending',
                total_files INTEGER DEFAULT 0,
                completed_files INTEGER DEFAULT 0,
                failed_files INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        ''')
        
        # 创建文件上传记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS file_uploads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER NOT NULL,
                file_path TEXT NOT NULL,
                file_name TEXT NOT NULL,
                file_size INTEGER,
                status TEXT DEFAULT 'pending',
                progress REAL DEFAULT 0,
                error_message TEXT,
                uploaded_at TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES upload_tasks (id)
            )
        ''')
        
        # 创建代理配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS proxy_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                enabled BOOLEAN DEFAULT 0,
                proxy_type TEXT NOT NULL,
                hostname TEXT NOT NULL,
                port INTEGER NOT NULL,
                username TEXT,
                password TEXT,
                is_default BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_account(self, name: str, api_id: int, api_hash: str, phone: str = None, session_string: str = None, account_type: str = 'user', bot_token: str = None) -> int:
        """添加账号"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO accounts (name, api_id, api_hash, phone, session_string, account_type, bot_token)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (name, api_id, api_hash, phone, session_string, account_type, bot_token))
        
        account_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return account_id
    
    def get_accounts(self) -> List[Dict]:
        """获取所有账号"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 使用显式的字段名查询，避免索引错误
        cursor.execute('''
            SELECT id, name, api_id, api_hash, phone, session_string, 
                   COALESCE(account_type, 'user') as account_type,
                   bot_token, is_active, created_at
            FROM accounts 
            WHERE is_active = 1
        ''')
        
        accounts = []
        for row in cursor.fetchall():
            accounts.append({
                'id': row[0],
                'name': row[1],
                'api_id': row[2],
                'api_hash': row[3],
                'phone': row[4],
                'session_string': row[5],
                'account_type': row[6] or 'user',  # 确保默认值
                'bot_token': row[7],
                'is_active': bool(row[8]),  # 确保是布尔值
                'created_at': row[9]
            })
        
        conn.close()
        return accounts

    def get_account(self, account_id: int) -> Dict:
        """获取单个账号信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name, api_id, api_hash, phone, session_string, account_type, bot_token, created_at
            FROM accounts WHERE id = ?
        ''', (account_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                'id': row[0],
                'name': row[1],
                'api_id': row[2],
                'api_hash': row[3],
                'phone': row[4],
                'session_string': row[5],
                'account_type': row[6],
                'bot_token': row[7],
                'created_at': row[8]
            }
        return None
    
    def update_session_string(self, account_id: int, session_string: str):
        """更新账号的session字符串"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE accounts SET session_string = ? WHERE id = ?
        ''', (session_string, account_id))
        
        conn.commit()
        conn.close()
    
    def delete_account(self, account_id: int):
        """删除账号"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM accounts WHERE id = ?', (account_id,))
        
        conn.commit()
        conn.close()
    
    def add_folder_history(self, path: str):
        """添加文件夹历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO folder_history (path, last_used)
            VALUES (?, CURRENT_TIMESTAMP)
        ''', (path,))
        
        conn.commit()
        conn.close()
    
    def get_folder_history(self, limit: int = 10) -> List[str]:
        """获取文件夹历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT path FROM folder_history
            ORDER BY last_used DESC
            LIMIT ?
        ''', (limit,))
        
        folders = [row[0] for row in cursor.fetchall()]
        conn.close()
        return folders
    
    def add_group_history(self, name: str, chat_id: str, account_id: int):
        """添加群组历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO group_history (name, chat_id, account_id, last_used)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ''', (name, chat_id, account_id))
        
        conn.commit()
        conn.close()
    
    def get_group_history(self, account_id: int, limit: int = 10) -> List[Dict]:
        """获取群组历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT name, chat_id FROM group_history
            WHERE account_id = ?
            ORDER BY last_used DESC
            LIMIT ?
        ''', (account_id, limit))

        groups = []
        for row in cursor.fetchall():
            # 返回符合Dialog格式的数据
            groups.append({
                'id': row[1],  # chat_id作为id
                'title': row[0],  # name作为title
                'type': 'history',  # 标记为历史记录
                'username': None,
                'is_info': False,
                'description': None
            })

        conn.close()
        return groups

    def add_bot_input_history(self, chat_id: str, account_id: int):
        """添加机器人输入历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO bot_input_history (chat_id, account_id, last_used)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        ''', (chat_id, account_id))

        conn.commit()
        conn.close()

    def get_bot_input_history(self, account_id: int, limit: int = 10) -> List[Dict]:
        """获取机器人输入历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT chat_id FROM bot_input_history
            WHERE account_id = ?
            ORDER BY last_used DESC
            LIMIT ?
        ''', (account_id, limit))

        inputs = []
        for row in cursor.fetchall():
            # 返回符合Dialog格式的数据
            inputs.append({
                'id': row[0],  # chat_id作为id
                'title': row[0],  # chat_id作为title
                'type': 'bot_history',  # 标记为机器人历史记录
                'username': None,
                'is_info': False,
                'description': None
            })

        conn.close()
        return inputs
    
    def create_upload_task(self, account_id: int, folder_path: str, target_chat_id: str, target_chat_name: str = None) -> int:
        """创建上传任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO upload_tasks (account_id, folder_path, target_chat_id, target_chat_name)
            VALUES (?, ?, ?, ?)
        ''', (account_id, folder_path, target_chat_id, target_chat_name))
        
        task_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return task_id
    
    def update_task_status(self, task_id: int, status: str, total_files: int = None, completed_files: int = None, failed_files: int = None):
        """更新任务状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        update_fields = ['status = ?', 'updated_at = CURRENT_TIMESTAMP']
        values = [status]
        
        if total_files is not None:
            update_fields.append('total_files = ?')
            values.append(total_files)
        
        if completed_files is not None:
            update_fields.append('completed_files = ?')
            values.append(completed_files)
        
        if failed_files is not None:
            update_fields.append('failed_files = ?')
            values.append(failed_files)
        
        values.append(task_id)
        
        cursor.execute(f'''
            UPDATE upload_tasks SET {', '.join(update_fields)}
            WHERE id = ?
        ''', values)
        
        conn.commit()
        conn.close()
    
    def get_active_tasks(self) -> List[Dict]:
        """获取活动任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ut.*, a.name as account_name
            FROM upload_tasks ut
            JOIN accounts a ON ut.account_id = a.id
            WHERE ut.status IN ('pending', 'running')
            ORDER BY ut.created_at DESC
        ''')
        
        tasks = []
        for row in cursor.fetchall():
            tasks.append({
                'id': row[0],
                'account_id': row[1],
                'folder_path': row[2],
                'target_chat_id': row[3],
                'target_chat_name': row[4],
                'status': row[5],
                'total_files': row[6],
                'completed_files': row[7],
                'failed_files': row[8],
                'created_at': row[9],
                'updated_at': row[10],
                'account_name': row[11]
            })
        
        conn.close()
        return tasks

    def get_all_tasks(self, limit: int = 100) -> List[Dict]:
        """获取所有任务（包括历史任务）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ut.*, a.name as account_name
            FROM upload_tasks ut
            JOIN accounts a ON ut.account_id = a.id
            ORDER BY ut.created_at DESC
            LIMIT ?
        ''', (limit,))
        
        tasks = []
        for row in cursor.fetchall():
            tasks.append({
                'id': row[0],
                'account_id': row[1],
                'folder_path': row[2],
                'target_chat_id': row[3],
                'target_chat_name': row[4],
                'status': row[5],
                'total_files': row[6],
                'completed_files': row[7],
                'failed_files': row[8],
                'created_at': row[9],
                'updated_at': row[10],
                'account_name': row[11]
            })
        
        conn.close()
        return tasks

    def delete_task(self, task_id: int):
        """删除任务及其相关文件记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 删除相关文件记录
        cursor.execute('DELETE FROM file_uploads WHERE task_id = ?', (task_id,))
        
        # 删除任务记录
        cursor.execute('DELETE FROM upload_tasks WHERE id = ?', (task_id,))
        
        conn.commit()
        conn.close()

    def get_task_by_id(self, task_id: int) -> Optional[Dict]:
        """根据ID获取任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ut.*, a.name as account_name
            FROM upload_tasks ut
            JOIN accounts a ON ut.account_id = a.id
            WHERE ut.id = ?
        ''', (task_id,))
        
        row = cursor.fetchone()
        if row:
            task = {
                'id': row[0],
                'account_id': row[1],
                'folder_path': row[2],
                'target_chat_id': row[3],
                'target_chat_name': row[4],
                'status': row[5],
                'total_files': row[6],
                'completed_files': row[7],
                'failed_files': row[8],
                'created_at': row[9],
                'updated_at': row[10],
                'account_name': row[11]
            }
            conn.close()
            return task
        
        conn.close()
        return None

    def get_incomplete_tasks(self) -> List[Dict]:
        """获取未完成的任务（用于应用启动时恢复）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ut.*, a.name as account_name
            FROM upload_tasks ut
            JOIN accounts a ON ut.account_id = a.id
            WHERE ut.status IN ('pending', 'running', 'paused')
            ORDER BY ut.created_at ASC
        ''')
        
        tasks = []
        for row in cursor.fetchall():
            tasks.append({
                'id': row[0],
                'account_id': row[1],
                'folder_path': row[2],
                'target_chat_id': row[3],
                'target_chat_name': row[4],
                'status': row[5],
                'total_files': row[6],
                'completed_files': row[7],
                'failed_files': row[8],
                'created_at': row[9],
                'updated_at': row[10],
                'account_name': row[11]
            })
        
        conn.close()
        return tasks
    
    def add_file_upload(self, task_id: int, file_path: str, file_name: str, file_size: int):
        """添加文件上传记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO file_uploads (task_id, file_path, file_name, file_size)
            VALUES (?, ?, ?, ?)
        ''', (task_id, file_path, file_name, file_size))
        
        file_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return file_id
    
    def update_file_upload_progress(self, file_id: int, progress: float, status: str = None, error_message: str = None):
        """更新文件上传进度"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        update_fields = ['progress = ?']
        values = [progress]
        
        if status is not None:
            update_fields.append('status = ?')
            values.append(status)
            
        if error_message is not None:
            update_fields.append('error_message = ?')
            values.append(error_message)
            
        if status == 'completed':
            update_fields.append('uploaded_at = CURRENT_TIMESTAMP')
        
        values.append(file_id)
        
        cursor.execute(f'''
            UPDATE file_uploads SET {', '.join(update_fields)}
            WHERE id = ?
        ''', values)
        
        conn.commit()
        conn.close()
    
    def get_task_files(self, task_id: int) -> List[Dict]:
        """获取任务的文件列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM file_uploads
            WHERE task_id = ?
            ORDER BY id
        ''', (task_id,))
        
        files = []
        for row in cursor.fetchall():
            files.append({
                'id': row[0],
                'task_id': row[1],
                'file_path': row[2],
                'file_name': row[3],
                'file_size': row[4],
                'status': row[5],
                'progress': row[6],
                'error_message': row[7],
                'uploaded_at': row[8]
            })
        
        conn.close()
        return files
    
    # 代理配置相关方法
    def add_proxy_config(self, name: str, proxy_type: str, hostname: str, port: int, 
                        username: str = None, password: str = None, enabled: bool = False, 
                        is_default: bool = False) -> int:
        """添加代理配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 如果设置为默认代理，先取消其他代理的默认状态
        if is_default:
            cursor.execute('UPDATE proxy_config SET is_default = 0')
        
        cursor.execute('''
            INSERT INTO proxy_config (name, proxy_type, hostname, port, username, password, enabled, is_default)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, proxy_type, hostname, port, username, password, enabled, is_default))
        
        proxy_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return proxy_id
    
    def get_proxy_configs(self) -> List[Dict]:
        """获取所有代理配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM proxy_config ORDER BY created_at DESC')
        configs = []
        for row in cursor.fetchall():
            configs.append({
                'id': row[0],
                'name': row[1],
                'enabled': bool(row[2]),
                'proxy_type': row[3],
                'hostname': row[4],
                'port': row[5],
                'username': row[6],
                'password': row[7],
                'is_default': bool(row[8]),
                'created_at': row[9],
                'updated_at': row[10]
            })
        
        conn.close()
        return configs
    
    def update_proxy_config(self, proxy_id: int, name: str = None, proxy_type: str = None, 
                           hostname: str = None, port: int = None, username: str = None, 
                           password: str = None, enabled: bool = None, is_default: bool = None):
        """更新代理配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 如果设置为默认代理，先取消其他代理的默认状态
        if is_default:
            cursor.execute('UPDATE proxy_config SET is_default = 0')
        
        update_fields = ['updated_at = CURRENT_TIMESTAMP']
        values = []
        
        if name is not None:
            update_fields.append('name = ?')
            values.append(name)
        if proxy_type is not None:
            update_fields.append('proxy_type = ?')
            values.append(proxy_type)
        if hostname is not None:
            update_fields.append('hostname = ?')
            values.append(hostname)
        if port is not None:
            update_fields.append('port = ?')
            values.append(port)
        if username is not None:
            update_fields.append('username = ?')
            values.append(username)
        if password is not None:
            update_fields.append('password = ?')
            values.append(password)
        if enabled is not None:
            update_fields.append('enabled = ?')
            values.append(enabled)
        if is_default is not None:
            update_fields.append('is_default = ?')
            values.append(is_default)
        
        values.append(proxy_id)
        
        cursor.execute(f'''
            UPDATE proxy_config SET {', '.join(update_fields)}
            WHERE id = ?
        ''', values)
        
        conn.commit()
        conn.close()
    
    def delete_proxy_config(self, proxy_id: int):
        """删除代理配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM proxy_config WHERE id = ?', (proxy_id,))
        
        conn.commit()
        conn.close()
    
    def get_default_proxy_config(self) -> Optional[Dict]:
        """获取默认代理配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM proxy_config WHERE is_default = 1 AND enabled = 1 LIMIT 1')
        row = cursor.fetchone()
        
        if row:
            config = {
                'id': row[0],
                'name': row[1],
                'enabled': bool(row[2]),
                'proxy_type': row[3],
                'hostname': row[4],
                'port': row[5],
                'username': row[6],
                'password': row[7],
                'is_default': bool(row[8]),
                'created_at': row[9],
                'updated_at': row[10]
            }
            conn.close()
            return config
        
        conn.close()
        return None 