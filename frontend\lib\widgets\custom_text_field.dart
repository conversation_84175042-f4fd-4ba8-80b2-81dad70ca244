import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextForm<PERSON>ield extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final String? labelText;
  final Widget? suffixIcon;
  final Function(String)? onChanged;
  final bool readOnly;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool enableSuggestions;
  final bool autocorrect;
  final InputDecoration? decoration;

  const CustomTextFormField({
    super.key,
    this.controller,
    this.hintText,
    this.labelText,
    this.suffixIcon,
    this.onChanged,
    this.readOnly = false,
    this.keyboardType,
    this.textInputAction,
    this.enableSuggestions = true,
    this.autocorrect = false,
    this.decoration,
  });

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      readOnly: widget.readOnly,
      keyboardType: widget.keyboardType ?? TextInputType.text,
      textInputAction: widget.textInputAction ?? TextInputAction.done,
      enableSuggestions: widget.enableSuggestions,
      autocorrect: widget.autocorrect,
      decoration: widget.decoration ??
          InputDecoration(
            border: const OutlineInputBorder(),
            hintText: widget.hintText,
            labelText: widget.labelText,
            suffixIcon: widget.suffixIcon,
          ),
      onChanged: widget.onChanged,
      enableIMEPersonalizedLearning: false,
      enableInteractiveSelection: true,
      contextMenuBuilder: (context, editableTextState) {
        return AdaptiveTextSelectionToolbar.editable(
          anchors: editableTextState.contextMenuAnchors,
          onCopy: editableTextState.cutEnabled
              ? () => editableTextState.copySelection(SelectionChangedCause.keyboard)
              : null,
          onCut: editableTextState.cutEnabled
              ? () => editableTextState.cutSelection(SelectionChangedCause.keyboard)
              : null,
          onPaste: editableTextState.pasteEnabled
              ? () => editableTextState.pasteText(SelectionChangedCause.keyboard)
              : null,
          onSelectAll: editableTextState.selectAllEnabled
              ? () => editableTextState.selectAll(SelectionChangedCause.keyboard)
              : null,
          clipboardStatus: ClipboardStatus.pasteable,
          onLookUp: null,
          onSearchWeb: null,
          onShare: null,
          onLiveTextInput: null,
        );
      },
    );
  }


} 