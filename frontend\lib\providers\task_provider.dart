import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../models/task.dart';
import 'dart:async';

class TaskProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  Timer? _refreshTimer;
  
  // 状态
  bool _isLoading = false;
  String? _error;
  
  // 数据
  List<UploadTask> _tasks = [];
  UploadTask? _selectedTask;
  
  // 筛选和排序
  String _statusFilter = 'all';
  String _sortBy = 'created_at';
  bool _sortAscending = false;
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  List<UploadTask> get tasks => _getFilteredAndSortedTasks();
  List<UploadTask> get allTasks => _tasks;
  UploadTask? get selectedTask => _selectedTask;
  
  String get statusFilter => _statusFilter;
  String get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;
  
  // 统计信息
  int get totalTasks => _tasks.length;
  int get pendingTasks => _tasks.where((task) => task.status == 'pending').length;
  int get runningTasks => _tasks.where((task) => task.status == 'running').length;
  int get pausedTasks => _tasks.where((task) => task.status == 'paused').length;
  int get completedTasks => _tasks.where((task) => 
    task.status == 'completed' || task.status == 'completed_with_errors').length;
  int get failedTasks => _tasks.where((task) => task.status == 'failed').length;
  
  TaskProvider() {
    _startAutoRefresh();
    loadTasks();
  }
  
  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }
  
  // 开始自动刷新
  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _refreshTasks();
    });
  }
  
  // 停止自动刷新
  void _stopAutoRefresh() {
    _refreshTimer?.cancel();
  }
  
  // 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // 设置错误
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  
  // 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  // 加载任务列表
  Future<void> loadTasks() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.getTasks();
      
      if (response.success) {
        _tasks = response.data ?? [];
        
        // 如果选中的任务不在列表中，清除选择
        if (_selectedTask != null && 
            !_tasks.any((task) => task.id == _selectedTask!.id)) {
          _selectedTask = null;
        }
        
        notifyListeners();
      } else {
        _setError(response.error);
      }
    } catch (e) {
      _setError('加载任务列表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  // 加载所有任务（包括历史任务）
  Future<void> loadAllTasks({int limit = 100}) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.getAllTasks(limit: limit);
      
      if (response.success) {
        _tasks = response.data ?? [];
        
        // 如果选中的任务不在列表中，清除选择
        if (_selectedTask != null && 
            !_tasks.any((task) => task.id == _selectedTask!.id)) {
          _selectedTask = null;
        }
        
        notifyListeners();
      } else {
        _setError(response.error);
      }
    } catch (e) {
      _setError('加载任务列表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  // 删除任务
  Future<bool> deleteTask(int taskId) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.deleteTask(taskId);
      
      if (response.success) {
        // 如果删除的是当前选中的任务，清除选择
        if (_selectedTask?.id == taskId) {
          _selectedTask = null;
        }
        
        // 重新加载任务列表
        await loadTasks();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('删除任务失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // 暂停任务
  Future<bool> pauseTask(int taskId) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.pauseTask(taskId);
      
      if (response.success) {
        // 重新加载任务列表
        await loadTasks();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('暂停任务失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // 恢复任务
  Future<bool> resumeTask(int taskId) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.resumeTask(taskId);
      
      if (response.success) {
        // 重新加载任务列表
        await loadTasks();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('恢复任务失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // 静默刷新任务列表
  Future<void> _refreshTasks() async {
    try {
      final response = await _apiService.getTasks();
      
      if (response.success) {
        _tasks = response.data ?? [];
        
        // 如果选中的任务不在列表中，清除选择
        if (_selectedTask != null && 
            !_tasks.any((task) => task.id == _selectedTask!.id)) {
          _selectedTask = null;
        }
        
        notifyListeners();
      }
    } catch (e) {
      // 静默处理错误
      if (kDebugMode) {
        print('刷新任务列表失败: $e');
      }
    }
  }
  
  // 开始上传任务
  Future<bool> startUpload({
    required int accountId,
    required String folderPath,
    required String targetChatId,
    String? targetChatName,
  }) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.startUpload(
        accountId: accountId,
        folderPath: folderPath,
        targetChatId: targetChatId,
        targetChatName: targetChatName,
      );
      
      if (response.success) {
        // 重新加载任务列表
        await loadTasks();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('启动上传失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // 获取任务详情
  Future<void> loadTaskDetail(int taskId) async {
    try {
      final response = await _apiService.getTaskDetail(taskId);
      
      if (response.success) {
        _selectedTask = response.data!;
        notifyListeners();
      } else {
        _setError(response.error);
      }
    } catch (e) {
      _setError('获取任务详情失败: $e');
    }
  }
  
  // 设置选中的任务
  void setSelectedTask(UploadTask? task) {
    _selectedTask = task;
    notifyListeners();
  }
  
  // 设置状态筛选
  void setStatusFilter(String filter) {
    _statusFilter = filter;
    notifyListeners();
  }
  
  // 设置排序方式
  void setSorting(String sortBy, bool ascending) {
    _sortBy = sortBy;
    _sortAscending = ascending;
    notifyListeners();
  }
  
  // 获取筛选和排序后的任务列表
  List<UploadTask> _getFilteredAndSortedTasks() {
    List<UploadTask> filteredTasks = _tasks;
    
    // 状态筛选
    if (_statusFilter != 'all') {
      filteredTasks = filteredTasks.where((task) => task.status == _statusFilter).toList();
    }
    
    // 排序
    filteredTasks.sort((a, b) {
      int comparison = 0;
      
      switch (_sortBy) {
        case 'created_at':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'updated_at':
          comparison = a.updatedAt.compareTo(b.updatedAt);
          break;
        case 'progress':
          comparison = a.progress.compareTo(b.progress);
          break;
        case 'total_files':
          comparison = a.totalFiles.compareTo(b.totalFiles);
          break;
        case 'account_name':
          comparison = a.accountName.compareTo(b.accountName);
          break;
        case 'folder_path':
          comparison = a.folderPath.compareTo(b.folderPath);
          break;
        default:
          comparison = a.createdAt.compareTo(b.createdAt);
      }
      
      return _sortAscending ? comparison : -comparison;
    });
    
    return filteredTasks;
  }
  
  // 获取任务统计
  Map<String, int> getTaskStats() {
    return {
      'total': totalTasks,
      'pending': pendingTasks,
      'running': runningTasks,
      'paused': pausedTasks,
      'completed': completedTasks,
      'failed': failedTasks,
    };
  }
  
  // 获取任务进度统计
  Map<String, dynamic> getProgressStats() {
    if (_tasks.isEmpty) {
      return {
        'total_files': 0,
        'completed_files': 0,
        'failed_files': 0,
        'overall_progress': 0.0,
      };
    }
    
    final totalFiles = _tasks.fold<int>(0, (sum, task) => sum + task.totalFiles);
    final completedFiles = _tasks.fold<int>(0, (sum, task) => sum + task.completedFiles);
    final failedFiles = _tasks.fold<int>(0, (sum, task) => sum + task.failedFiles);
    
    final overallProgress = totalFiles > 0 ? completedFiles / totalFiles : 0.0;
    
    return {
      'total_files': totalFiles,
      'completed_files': completedFiles,
      'failed_files': failedFiles,
      'overall_progress': overallProgress,
    };
  }
  
  // 获取活跃任务
  List<UploadTask> getActiveTasks() {
    return _tasks.where((task) => 
      task.status == 'pending' || task.status == 'running' || task.status == 'paused').toList();
  }
  
  // 获取已完成任务
  List<UploadTask> getCompletedTasks() {
    return _tasks.where((task) => 
      task.status == 'completed' || task.status == 'completed_with_errors').toList();
  }
  
  // 获取失败任务
  List<UploadTask> getFailedTasks() {
    return _tasks.where((task) => task.status == 'failed').toList();
  }
  
  // 根据账号ID获取任务
  List<UploadTask> getTasksByAccount(int accountId) {
    return _tasks.where((task) => task.accountId == accountId).toList();
  }
  
  // 搜索任务
  List<UploadTask> searchTasks(String query) {
    if (query.isEmpty) return tasks;
    
    final lowercaseQuery = query.toLowerCase();
    return tasks.where((task) {
      return task.accountName.toLowerCase().contains(lowercaseQuery) ||
             task.folderPath.toLowerCase().contains(lowercaseQuery) ||
             (task.targetChatName?.toLowerCase().contains(lowercaseQuery) ?? false) ||
             task.targetChatId.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
  
  // 获取任务文件统计
  Map<String, dynamic> getTaskFileStats(UploadTask task) {
    final files = task.files ?? [];
    final pending = files.where((f) => f.status == 'pending').length;
    final running = files.where((f) => f.status == 'running').length;
    final completed = files.where((f) => f.status == 'completed').length;
    final failed = files.where((f) => f.status == 'failed').length;
    
    return {
      'pending': pending,
      'running': running,
      'completed': completed,
      'failed': failed,
      'total': files.length,
    };
  }
  
  // 格式化文件大小
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  // 格式化时间
  String formatTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inMinutes < 1) {
        return '刚刚';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}小时前';
      } else {
        return '${difference.inDays}天前';
      }
    } catch (e) {
      return isoString;
    }
  }
  
  // 启用/禁用自动刷新
  void setAutoRefresh(bool enabled) {
    if (enabled) {
      _startAutoRefresh();
    } else {
      _stopAutoRefresh();
    }
  }
} 