// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadTask _$UploadTaskFromJson(Map<String, dynamic> json) => UploadTask(
      id: (json['id'] as num).toInt(),
      accountId: (json['account_id'] as num).toInt(),
      folderPath: json['folder_path'] as String,
      targetChatId: json['target_chat_id'] as String,
      targetChatName: json['target_chat_name'] as String?,
      status: json['status'] as String,
      totalFiles: (json['total_files'] as num).toInt(),
      completedFiles: (json['completed_files'] as num).toInt(),
      failedFiles: (json['failed_files'] as num).toInt(),
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      accountName: json['account_name'] as String,
      files: (json['files'] as List<dynamic>?)
          ?.map((e) => FileUpload.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UploadTaskToJson(UploadTask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'account_id': instance.accountId,
      'folder_path': instance.folderPath,
      'target_chat_id': instance.targetChatId,
      'target_chat_name': instance.targetChatName,
      'status': instance.status,
      'total_files': instance.totalFiles,
      'completed_files': instance.completedFiles,
      'failed_files': instance.failedFiles,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'account_name': instance.accountName,
      'files': instance.files,
    };

FileUpload _$FileUploadFromJson(Map<String, dynamic> json) => FileUpload(
      id: (json['id'] as num).toInt(),
      taskId: (json['task_id'] as num).toInt(),
      filePath: json['file_path'] as String,
      fileName: json['file_name'] as String,
      fileSize: (json['file_size'] as num).toInt(),
      status: json['status'] as String,
      progress: (json['progress'] as num).toDouble(),
      errorMessage: json['error_message'] as String?,
      uploadedAt: json['uploaded_at'] as String?,
    );

Map<String, dynamic> _$FileUploadToJson(FileUpload instance) =>
    <String, dynamic>{
      'id': instance.id,
      'task_id': instance.taskId,
      'file_path': instance.filePath,
      'file_name': instance.fileName,
      'file_size': instance.fileSize,
      'status': instance.status,
      'progress': instance.progress,
      'error_message': instance.errorMessage,
      'uploaded_at': instance.uploadedAt,
    };

UploadStats _$UploadStatsFromJson(Map<String, dynamic> json) => UploadStats(
      totalSpeed: (json['total_speed'] as num).toDouble(),
      activeUploads: (json['active_uploads'] as num).toInt(),
      totalUploaded: (json['total_uploaded'] as num).toInt(),
      totalSize: (json['total_size'] as num).toInt(),
      currentSpeed: (json['current_speed'] as num).toDouble(),
    );

Map<String, dynamic> _$UploadStatsToJson(UploadStats instance) =>
    <String, dynamic>{
      'total_speed': instance.totalSpeed,
      'active_uploads': instance.activeUploads,
      'total_uploaded': instance.totalUploaded,
      'total_size': instance.totalSize,
      'current_speed': instance.currentSpeed,
    };
