#!/usr/bin/env python3
"""
Telegram Upload Tool Launcher
同时启动前端和后端服务
"""

import subprocess
import sys
import os
import time
import threading
import signal
import requests
from pathlib import Path

class TelegramUploadLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
        # 获取当前脚本所在目录
        self.script_dir = Path(__file__).parent.absolute()
        self.backend_path = self.script_dir / "backend" / "dist" / "telegram_backend" / "telegram_backend.exe"
        self.frontend_path = self.script_dir / "frontend" / "build" / "windows" / "x64" / "runner" / "Release" / "telegram_uploader.exe"
        
    def check_backend_health(self, timeout=30):
        """检查后端是否启动成功"""
        print("等待后端服务启动...")
        for i in range(timeout):
            try:
                response = requests.get("http://localhost:8090/api/health", timeout=1)
                if response.status_code == 200:
                    print("✓ 后端服务启动成功")
                    return True
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
            print(f"等待中... ({i+1}/{timeout})")
        return False
    
    def start_backend(self):
        """启动后端服务"""
        if not self.backend_path.exists():
            print(f"❌ 后端可执行文件不存在: {self.backend_path}")
            return False
            
        print("🚀 启动后端服务...")
        try:
            self.backend_process = subprocess.Popen(
                [str(self.backend_path)],
                cwd=str(self.backend_path.parent),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # 检查后端是否启动成功
            if self.check_backend_health():
                return True
            else:
                print("❌ 后端服务启动失败")
                self.stop_backend()
                return False
                
        except Exception as e:
            print(f"❌ 启动后端服务时出错: {e}")
            return False
    
    def start_frontend(self):
        """启动前端应用"""
        if not self.frontend_path.exists():
            print(f"❌ 前端可执行文件不存在: {self.frontend_path}")
            return False
            
        print("🖥️ 启动前端应用...")
        try:
            self.frontend_process = subprocess.Popen(
                [str(self.frontend_path)],
                cwd=str(self.frontend_path.parent)
            )
            print("✓ 前端应用启动成功")
            return True
        except Exception as e:
            print(f"❌ 启动前端应用时出错: {e}")
            return False
    
    def stop_backend(self):
        """停止后端服务"""
        if self.backend_process:
            print("🛑 停止后端服务...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
            self.backend_process = None
    
    def stop_frontend(self):
        """停止前端应用"""
        if self.frontend_process:
            print("🛑 停止前端应用...")
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
            self.frontend_process = None
    
    def signal_handler(self, signum, frame):
        """处理系统信号"""
        print("\n🛑 收到退出信号，正在关闭应用...")
        self.running = False
        self.stop_frontend()
        self.stop_backend()
        sys.exit(0)
    
    def run(self):
        """运行应用"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("=" * 50)
        print("🚀 Telegram Upload Tool 启动器")
        print("=" * 50)
        
        # 启动后端服务
        if not self.start_backend():
            print("❌ 无法启动后端服务，程序退出")
            sys.exit(1)
        
        # 启动前端应用
        if not self.start_frontend():
            print("❌ 无法启动前端应用，程序退出")
            self.stop_backend()
            sys.exit(1)
        
        print("✅ 应用启动完成！")
        print("💡 关闭前端窗口将自动退出整个应用")
        
        try:
            # 等待前端进程结束
            self.frontend_process.wait()
        except KeyboardInterrupt:
            pass
        finally:
            print("\n🛑 应用正在关闭...")
            self.stop_backend()
            print("✅ 应用已关闭")

if __name__ == "__main__":
    launcher = TelegramUploadLauncher()
    launcher.run() 