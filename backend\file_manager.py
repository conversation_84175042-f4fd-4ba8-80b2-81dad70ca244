import os
import mimetypes
from typing import List, Dict, Optional
from pathlib import Path
import hashlib
import time
import logging

# 添加日志配置
logger = logging.getLogger(__name__)

class FileManager:
    def __init__(self):
        # 支持的视频格式（用于分类显示）
        self.video_extensions = {
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', 
            '.m4v', '.3gp', '.3g2', '.f4v', '.f4p', '.f4a', '.f4b',
            '.mpg', '.mpeg', '.ts', '.vob', '.rm', '.rmvb', '.asf'
        }
        
        # 支持的音频格式（用于分类显示）
        self.audio_extensions = {
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
            '.opus', '.aiff', '.au', '.ra', '.mka', '.ape', '.dts',
            '.ac3', '.amr', '.mid', '.midi'
        }
        
        # 支持的图片格式
        self.image_extensions = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif',
            '.webp', '.svg', '.ico', '.heic', '.heif'
        }
        
        # 支持的文档格式
        self.document_extensions = {
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.txt', '.rtf', '.odt', '.ods', '.odp', '.csv'
        }
        
        # 支持的压缩格式
        self.archive_extensions = {
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.tar.gz', '.tar.bz2'
        }
        
        # 不建议上传的系统文件格式（跳过这些文件）
        self.skip_extensions = {
            '.tmp', '.temp', '.log', '.cache', '.bak', '.swp', '.lock',
            '.ds_store', '.thumbs.db', '.desktop.ini'
        }
        
        # 所有支持的格式（现在支持所有文件，除了需要跳过的）
        self.supported_extensions = (self.video_extensions | self.audio_extensions | 
                                   self.image_extensions | self.document_extensions | 
                                   self.archive_extensions)
    
    def scan_folder(self, folder_path: str, include_subdirs: bool = True) -> List[Dict]:
        """扫描文件夹中的所有文件"""
        if not os.path.exists(folder_path):
            raise FileNotFoundError(f"文件夹不存在: {folder_path}")
        
        if not os.path.isdir(folder_path):
            raise NotADirectoryError(f"路径不是文件夹: {folder_path}")
        
        files = []
        total_scanned = 0
        skipped_files = 0
        
        logger.info(f"开始扫描文件夹: {folder_path}, 包含子目录: {include_subdirs}")
        
        if include_subdirs:
            # 递归扫描子目录
            for root, dirs, filenames in os.walk(folder_path):
                for filename in filenames:
                    total_scanned += 1
                    file_path = os.path.join(root, filename)
                    file_info = self._get_file_info(file_path)
                    if file_info:
                        files.append(file_info)
                    else:
                        skipped_files += 1
        else:
            # 只扫描当前目录
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    total_scanned += 1
                    file_info = self._get_file_info(file_path)
                    if file_info:
                        files.append(file_info)
                    else:
                        skipped_files += 1
        
        # 按文件名排序
        files.sort(key=lambda x: x['name'])
        
        logger.info(f"扫描完成: 总文件数 {total_scanned}, 支持的文件 {len(files)}, 跳过的文件 {skipped_files}")
        
        return files
    
    def _get_file_info(self, file_path: str) -> Optional[Dict]:
        """获取文件信息"""
        try:
            # 获取文件扩展名
            _, ext = os.path.splitext(file_path)
            ext_lower = ext.lower()
            
            # 跳过系统文件和临时文件
            if ext_lower in self.skip_extensions:
                return None
            
            # 跳过隐藏文件（以.开头的文件，除非有扩展名）
            filename = os.path.basename(file_path)
            if filename.startswith('.') and not ext:
                return None
            
            # 获取文件统计信息
            stat = os.stat(file_path)
            
            # 确定文件类型
            file_type = self._get_file_type(ext_lower)
            
            # 解析文件名
            a_part, b_part = self.parse_filename(filename)
            
            return {
                'path': file_path,
                'name': filename,
                'size': stat.st_size,
                'size_formatted': self.format_file_size(stat.st_size),
                'type': file_type,
                'extension': ext_lower,
                'modified_time': stat.st_mtime,
                'modified_time_formatted': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime)),
                'a_part': a_part,
                'b_part': b_part,
                'caption': self.generate_caption(filename),
                'hash': self.get_file_hash(file_path)
            }
        
        except Exception as e:
            logger.warning(f"获取文件信息失败 {file_path}: {e}")
            return None
    
    def _get_file_type(self, ext_lower: str) -> str:
        """根据扩展名确定文件类型"""
        if ext_lower in self.video_extensions:
            return "video"
        elif ext_lower in self.audio_extensions:
            return "audio"
        elif ext_lower in self.image_extensions:
            return "image"
        elif ext_lower in self.document_extensions:
            return "document"
        elif ext_lower in self.archive_extensions:
            return "archive"
        else:
            return "other"
    
    def parse_filename(self, filename: str) -> tuple:
        """解析文件名，返回(A, B)"""
        name_without_ext = os.path.splitext(filename)[0]
        # 找到第一个空格的位置
        space_index = name_without_ext.find(' ')
        if space_index != -1:
            a_part = name_without_ext[:space_index]
            b_part = name_without_ext[space_index + 1:]
            return a_part, b_part
        else:
            return name_without_ext, ""
    
    def generate_caption(self, filename: str) -> str:
        """生成文件描述"""
        a_part, b_part = self.parse_filename(filename)
        if b_part:
            return f"#{a_part} {b_part}"
        else:
            return f"#{a_part}"
    
    def get_file_hash(self, file_path: str, chunk_size: int = 8192) -> str:
        """获取文件的MD5哈希值（仅读取前1MB以提高性能）"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                # 只读取前1MB来计算哈希，提高性能
                data = f.read(1024 * 1024)
                hash_md5.update(data)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size/1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size/(1024*1024):.1f} MB"
        else:
            return f"{size/(1024*1024*1024):.1f} GB"
    
    def get_folder_stats(self, folder_path: str) -> Dict:
        """获取文件夹统计信息"""
        if not os.path.exists(folder_path):
            return {'error': '文件夹不存在'}
        
        # 添加调试信息：统计所有文件
        total_files_count = 0
        try:
            for root, dirs, filenames in os.walk(folder_path):
                total_files_count += len(filenames)
        except Exception as e:
            logger.error(f"统计文件夹总文件数失败: {e}")
        
        files = self.scan_folder(folder_path)
        
        total_size = sum(f['size'] for f in files)
        video_count = sum(1 for f in files if f['type'] == 'video')
        audio_count = sum(1 for f in files if f['type'] == 'audio')
        image_count = sum(1 for f in files if f['type'] == 'image')
        document_count = sum(1 for f in files if f['type'] == 'document')
        archive_count = sum(1 for f in files if f['type'] == 'archive')
        other_count = sum(1 for f in files if f['type'] == 'other')
        
        stats = {
            'total_files': len(files),
            'video_count': video_count,
            'audio_count': audio_count,
            'image_count': image_count,
            'document_count': document_count,
            'archive_count': archive_count,
            'other_count': other_count,
            'total_size': total_size,
            'total_size_formatted': self.format_file_size(total_size),
            'folder_path': folder_path,
            'total_files_in_folder': total_files_count,
            'supported_extensions': {
                'video': sorted(list(self.video_extensions)),
                'audio': sorted(list(self.audio_extensions)),
                'image': sorted(list(self.image_extensions)),
                'document': sorted(list(self.document_extensions)),
                'archive': sorted(list(self.archive_extensions)),
                'skip': sorted(list(self.skip_extensions))
            }
        }
        
        logger.info(f"文件夹统计 - 路径: {folder_path}, 总文件: {total_files_count}, 支持的文件: {len(files)} "
                   f"(视频:{video_count}, 音频:{audio_count}, 图片:{image_count}, 文档:{document_count}, "
                   f"压缩:{archive_count}, 其他:{other_count})")
        
        return stats
    
    def validate_folder(self, folder_path: str) -> Dict:
        """验证文件夹是否有效"""
        if not folder_path:
            return {'valid': False, 'error': '文件夹路径不能为空'}
        
        if not os.path.exists(folder_path):
            return {'valid': False, 'error': '文件夹不存在'}
        
        if not os.path.isdir(folder_path):
            return {'valid': False, 'error': '路径不是文件夹'}
        
        try:
            # 尝试读取文件夹内容
            os.listdir(folder_path)
        except PermissionError:
            return {'valid': False, 'error': '没有权限访问该文件夹'}
        except Exception as e:
            return {'valid': False, 'error': f'访问文件夹失败: {str(e)}'}
        
        return {'valid': True}
    
    def get_recent_folders(self, limit: int = 10) -> List[str]:
        """获取最近使用的文件夹（从数据库）"""
        # 这个方法需要在主应用中实现，因为需要访问数据库
        pass
    
    def filter_files(self, files: List[Dict], file_type: str = None, min_size: int = None, max_size: int = None) -> List[Dict]:
        """筛选文件"""
        filtered_files = files
        
        if file_type and file_type != 'all':
            filtered_files = [f for f in filtered_files if f['type'] == file_type]
        
        if min_size is not None:
            filtered_files = [f for f in filtered_files if f['size'] >= min_size]
        
        if max_size is not None:
            filtered_files = [f for f in filtered_files if f['size'] <= max_size]
        
        return filtered_files
    
    def search_files(self, files: List[Dict], query: str) -> List[Dict]:
        """搜索文件"""
        if not query:
            return files
        
        query = query.lower()
        return [
            f for f in files
            if query in f['name'].lower() or 
               query in f['a_part'].lower() or 
               query in f['b_part'].lower()
        ] 