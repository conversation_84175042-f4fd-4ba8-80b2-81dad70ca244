{"inputs": ["E:\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "E:\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_windows.dll", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_export.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_messenger.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\flutter_windows.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\icudtl.dat", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "E:\\github\\local_upload\\frontend\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}