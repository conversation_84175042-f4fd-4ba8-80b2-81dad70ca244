import 'package:json_annotation/json_annotation.dart';

part 'account.g.dart';

@JsonSerializable()
class Account {
  final int id;
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'api_id')
  final int apiId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'api_hash')
  final String apiHash;
  final String? phone;
  @J<PERSON><PERSON><PERSON>(name: 'session_string')
  final String? sessionString;
  @J<PERSON><PERSON><PERSON>(name: 'account_type')
  final String accountType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'bot_token')
  final String? botToken;
  @Json<PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @Json<PERSON>ey(name: 'created_at')
  final String createdAt;

  Account({
    required this.id,
    required this.name,
    required this.apiId,
    required this.apiHash,
    this.phone,
    this.sessionString,
    this.accountType = 'user',
    this.botToken,
    required this.isActive,
    required this.createdAt,
  });

  factory Account.fromJson(Map<String, dynamic> json) {
    // 处理 is_active 字段的类型转换
    if (json['is_active'] is int) {
      json = Map<String, dynamic>.from(json);
      json['is_active'] = json['is_active'] == 1;
    }
    return _$AccountFromJson(json);
  }
  
  Map<String, dynamic> toJson() => _$AccountToJson(this);

  // 重写 equals 和 hashCode 方法，基于 id 字段比较
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Account &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@JsonSerializable()
class Dialog {
  final String id;
  final String title;
  final String type;
  final String? username;
  @JsonKey(name: 'is_info')
  final bool? isInfo;
  final String? description;

  Dialog({
    required this.id,
    required this.title,
    required this.type,
    this.username,
    this.isInfo,
    this.description,
  });

  factory Dialog.fromJson(Map<String, dynamic> json) => _$DialogFromJson(json);
  Map<String, dynamic> toJson() => _$DialogToJson(this);

  // 重写 equals 和 hashCode 方法，基于 id 字段比较
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Dialog &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
} 