import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:percent_indicator/percent_indicator.dart';
import '../providers/task_provider.dart';
import '../models/task.dart';

class TaskList extends StatelessWidget {
  const TaskList({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TaskProvider>(
      builder: (context, taskProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    '任务列表',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const Spacer(),
                  
                  // 查看历史任务按钮
                  TextButton.icon(
                    onPressed: () => _showAllTasks(context, taskProvider),
                    icon: const Icon(Icons.history),
                    label: const Text('查看历史'),
                  ),
                  const SizedBox(width: 8),
                  
                  // 筛选按钮
                  DropdownButton<String>(
                    value: taskProvider.statusFilter,
                    onChanged: (String? value) {
                      if (value != null) {
                        taskProvider.setStatusFilter(value);
                      }
                    },
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('全部')),
                      DropdownMenuItem(value: 'pending', child: Text('等待中')),
                      DropdownMenuItem(value: 'running', child: Text('运行中')),
                      DropdownMenuItem(value: 'paused', child: Text('已暂停')),
                      DropdownMenuItem(value: 'completed', child: Text('已完成')),
                      DropdownMenuItem(value: 'failed', child: Text('失败')),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              if (taskProvider.tasks.isEmpty) ...[
                Expanded(
                  child: Center(
                    child: Text(
                      '暂无任务',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ] else ...[
                Expanded(
                  child: ListView.builder(
                    itemCount: taskProvider.tasks.length,
                    itemBuilder: (context, index) {
                      final task = taskProvider.tasks[index];
                      return _buildTaskCard(context, task);
                    },
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildTaskCard(BuildContext context, UploadTask task) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 任务标题
            Row(
              children: [
                Icon(
                  _getStatusIcon(task.status),
                  color: _getStatusColor(task.status),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    task.targetChatName ?? task.targetChatId,
                    style: Theme.of(context).textTheme.titleMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Chip(
                  label: Text(
                    _getStatusText(task.status),
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: _getStatusColor(task.status).withOpacity(0.1),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // 任务信息
            Text(
              '账号: ${task.accountName}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              '文件夹: ${task.folderPath}',
              style: Theme.of(context).textTheme.bodySmall,
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              '创建时间: ${_formatTime(task.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            
            const SizedBox(height: 12),
            
            // 进度条
            if (task.status == 'running' || task.status == 'completed' || task.status == 'completed_with_errors') ...[
              LayoutBuilder(
                builder: (context, constraints) {
                  return LinearPercentIndicator(
                    width: constraints.maxWidth,
                    lineHeight: 8,
                    percent: task.progress,
                    backgroundColor: Colors.grey[300],
                    progressColor: _getStatusColor(task.status),
                    barRadius: const Radius.circular(4),
                  );
                },
              ),
              const SizedBox(height: 8),
              
              // 进度文本
              Row(
                children: [
                  Text(
                    '${task.progressText} (${(task.progress * 100).toStringAsFixed(1)}%)',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  if (task.failedFiles > 0)
                    Text(
                      '失败: ${task.failedFiles}',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ],
            
            if (task.status == 'pending') ...[
              Text(
                '等待开始...',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 12,
                ),
              ),
            ],
            
            if (task.status == 'failed') ...[
              Text(
                '任务失败',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ],
            
            // 任务操作按钮行
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _buildTaskActions(context, task),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskActions(BuildContext context, UploadTask task) {
    final taskProvider = Provider.of<TaskProvider>(context, listen: false);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 暂停/恢复按钮
        if (task.status == 'running') ...[
          IconButton(
            onPressed: () => _pauseTask(context, taskProvider, task.id),
            icon: const Icon(Icons.pause),
            tooltip: '暂停任务',
            color: Colors.orange,
          ),
        ] else if (task.status == 'paused' || task.status == 'failed') ...[
          IconButton(
            onPressed: () => _resumeTask(context, taskProvider, task.id),
            icon: const Icon(Icons.play_arrow),
            tooltip: '恢复任务',
            color: Colors.green,
          ),
        ],
        
        // 删除按钮
        IconButton(
          onPressed: () => _deleteTask(context, taskProvider, task),
          icon: const Icon(Icons.delete),
          tooltip: '删除任务',
          color: Colors.red,
        ),
      ],
    );
  }

  Future<void> _showAllTasks(BuildContext context, TaskProvider taskProvider) async {
    await taskProvider.loadAllTasks();
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('已加载所有任务（包括历史任务）'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _deleteTask(BuildContext context, TaskProvider taskProvider, UploadTask task) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除任务 "${task.targetChatName ?? task.targetChatId}" 吗？此操作不可逆。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      final success = await taskProvider.deleteTask(task.id);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '任务已删除' : '删除任务失败: ${taskProvider.error}'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pauseTask(BuildContext context, TaskProvider taskProvider, int taskId) async {
    final success = await taskProvider.pauseTask(taskId);
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? '任务已暂停' : '暂停任务失败: ${taskProvider.error}'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _resumeTask(BuildContext context, TaskProvider taskProvider, int taskId) async {
    final success = await taskProvider.resumeTask(taskId);
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? '任务已恢复' : '恢复任务失败: ${taskProvider.error}'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
  
  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'running':
        return Icons.play_arrow;
      case 'paused':
        return Icons.pause;
      case 'completed':
        return Icons.check_circle;
      case 'completed_with_errors':
        return Icons.warning;
      case 'failed':
        return Icons.error;
      default:
        return Icons.help;
    }
  }
  
  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'running':
        return Colors.blue;
      case 'paused':
        return Colors.purple;
      case 'completed':
        return Colors.green;
      case 'completed_with_errors':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return '等待中';
      case 'running':
        return '运行中';
      case 'paused':
        return '已暂停';
      case 'completed':
        return '已完成';
      case 'completed_with_errors':
        return '部分失败';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  }
  
  String _formatTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      return '${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return isoString;
    }
  }
} 