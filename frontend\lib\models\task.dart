import 'package:json_annotation/json_annotation.dart';

part 'task.g.dart';

@JsonSerializable()
class UploadTask {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'account_id')
  final int accountId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'folder_path')
  final String folderPath;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'target_chat_id')
  final String targetChatId;
  @J<PERSON><PERSON><PERSON>(name: 'target_chat_name')
  final String? targetChatName;
  final String status;
  @<PERSON>son<PERSON>ey(name: 'total_files')
  final int totalFiles;
  @<PERSON>son<PERSON>ey(name: 'completed_files')
  final int completedFiles;
  @<PERSON>son<PERSON>ey(name: 'failed_files')
  final int failedFiles;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String updatedAt;
  @Json<PERSON>ey(name: 'account_name')
  final String accountName;
  final List<FileUpload>? files;

  UploadTask({
    required this.id,
    required this.accountId,
    required this.folderPath,
    required this.targetChatId,
    this.targetChatName,
    required this.status,
    required this.totalFiles,
    required this.completedFiles,
    required this.failedFiles,
    required this.createdAt,
    required this.updatedAt,
    required this.accountName,
    this.files,
  });

  factory UploadTask.fromJson(Map<String, dynamic> json) => _$UploadTaskFromJson(json);
  Map<String, dynamic> toJson() => _$UploadTaskToJson(this);

  double get progress {
    if (totalFiles == 0) return 0.0;
    final result = completedFiles / totalFiles;
    return result.clamp(0.0, 1.0);
  }

  String get progressText {
    return '$completedFiles / $totalFiles';
  }
}

@JsonSerializable()
class FileUpload {
  final int id;
  @JsonKey(name: 'task_id')
  final int taskId;
  @JsonKey(name: 'file_path')
  final String filePath;
  @JsonKey(name: 'file_name')
  final String fileName;
  @JsonKey(name: 'file_size')
  final int fileSize;
  final String status;
  final double progress;
  @JsonKey(name: 'error_message')
  final String? errorMessage;
  @JsonKey(name: 'uploaded_at')
  final String? uploadedAt;

  FileUpload({
    required this.id,
    required this.taskId,
    required this.filePath,
    required this.fileName,
    required this.fileSize,
    required this.status,
    required this.progress,
    this.errorMessage,
    this.uploadedAt,
  });

  factory FileUpload.fromJson(Map<String, dynamic> json) => _$FileUploadFromJson(json);
  Map<String, dynamic> toJson() => _$FileUploadToJson(this);
}

@JsonSerializable()
class UploadStats {
  @JsonKey(name: 'total_speed')
  final double totalSpeed;
  @JsonKey(name: 'active_uploads')
  final int activeUploads;
  @JsonKey(name: 'total_uploaded')
  final int totalUploaded;
  @JsonKey(name: 'total_size')
  final int totalSize;
  @JsonKey(name: 'current_speed')
  final double currentSpeed;

  UploadStats({
    required this.totalSpeed,
    required this.activeUploads,
    required this.totalUploaded,
    required this.totalSize,
    required this.currentSpeed,
  });

  factory UploadStats.fromJson(Map<String, dynamic> json) => _$UploadStatsFromJson(json);
  Map<String, dynamic> toJson() => _$UploadStatsToJson(this);

  String get speedFormatted {
    return formatSpeed(currentSpeed);
  }

  String formatSpeed(double speed) {
    if (speed < 1024) {
      return '${speed.toStringAsFixed(1)} B/s';
    } else if (speed < 1024 * 1024) {
      return '${(speed / 1024).toStringAsFixed(1)} KB/s';
    } else if (speed < 1024 * 1024 * 1024) {
      return '${(speed / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    } else {
      return '${(speed / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB/s';
    }
  }
} 