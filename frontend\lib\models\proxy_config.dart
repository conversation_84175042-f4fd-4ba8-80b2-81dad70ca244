import 'package:json_annotation/json_annotation.dart';

part 'proxy_config.g.dart';

@JsonSerializable()
class ProxyConfig {
  final int id;
  final String name;
  final bool enabled;
  @Json<PERSON><PERSON>(name: 'proxy_type')
  final String proxyType;
  final String hostname;
  final int port;
  final String? username;
  final String? password;
  @J<PERSON><PERSON><PERSON>(name: 'is_default')
  final bool isDefault;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String updatedAt;

  ProxyConfig({
    required this.id,
    required this.name,
    required this.enabled,
    required this.proxyType,
    required this.hostname,
    required this.port,
    this.username,
    this.password,
    required this.isDefault,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProxyConfig.fromJson(Map<String, dynamic> json) => _$ProxyConfigFromJson(json);
  Map<String, dynamic> toJson() => _$ProxyConfigToJson(this);

  String get displayAddress => '$hostname:$port';
  
  String get typeDisplayName {
    switch (proxyType.toUpperCase()) {
      case 'HTTP':
        return 'HTTP 代理';
      case 'SOCKS4':
        return 'SOCKS4 代理';
      case 'SOCKS5':
        return 'SOCKS5 代理';
      default:
        return proxyType;
    }
  }
}

@JsonSerializable()
class ProxyTestResult {
  final bool success;
  final String message;
  final int? latency;

  ProxyTestResult({
    required this.success,
    required this.message,
    this.latency,
  });

  factory ProxyTestResult.fromJson(Map<String, dynamic> json) => _$ProxyTestResultFromJson(json);
  Map<String, dynamic> toJson() => _$ProxyTestResultToJson(this);
} 