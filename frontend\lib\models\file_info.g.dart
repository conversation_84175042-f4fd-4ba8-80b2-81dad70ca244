// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FileInfo _$FileInfoFromJson(Map<String, dynamic> json) => FileInfo(
      path: json['path'] as String,
      name: json['name'] as String,
      size: (json['size'] as num).toInt(),
      sizeFormatted: json['size_formatted'] as String,
      type: json['type'] as String,
      extension: json['extension'] as String,
      modifiedTime: (json['modified_time'] as num).toDouble(),
      modifiedTimeFormatted: json['modified_time_formatted'] as String,
      aPart: json['a_part'] as String,
      bPart: json['b_part'] as String,
      caption: json['caption'] as String,
      hash: json['hash'] as String,
    );

Map<String, dynamic> _$FileInfoToJson(FileInfo instance) => <String, dynamic>{
      'path': instance.path,
      'name': instance.name,
      'size': instance.size,
      'size_formatted': instance.sizeFormatted,
      'type': instance.type,
      'extension': instance.extension,
      'modified_time': instance.modifiedTime,
      'modified_time_formatted': instance.modifiedTimeFormatted,
      'a_part': instance.aPart,
      'b_part': instance.bPart,
      'caption': instance.caption,
      'hash': instance.hash,
    };

FolderStats _$FolderStatsFromJson(Map<String, dynamic> json) => FolderStats(
      totalFiles: (json['total_files'] as num).toInt(),
      videoCount: (json['video_count'] as num).toInt(),
      audioCount: (json['audio_count'] as num).toInt(),
      imageCount: (json['image_count'] as num?)?.toInt(),
      documentCount: (json['document_count'] as num?)?.toInt(),
      archiveCount: (json['archive_count'] as num?)?.toInt(),
      otherCount: (json['other_count'] as num?)?.toInt(),
      totalSize: (json['total_size'] as num).toInt(),
      totalSizeFormatted: json['total_size_formatted'] as String,
      folderPath: json['folder_path'] as String,
      totalFilesInFolder: (json['total_files_in_folder'] as num?)?.toInt(),
      supportedExtensions:
          json['supported_extensions'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$FolderStatsToJson(FolderStats instance) =>
    <String, dynamic>{
      'total_files': instance.totalFiles,
      'video_count': instance.videoCount,
      'audio_count': instance.audioCount,
      'image_count': instance.imageCount,
      'document_count': instance.documentCount,
      'archive_count': instance.archiveCount,
      'other_count': instance.otherCount,
      'total_size': instance.totalSize,
      'total_size_formatted': instance.totalSizeFormatted,
      'folder_path': instance.folderPath,
      'total_files_in_folder': instance.totalFilesInFolder,
      'supported_extensions': instance.supportedExtensions,
    };
