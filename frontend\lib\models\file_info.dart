import 'package:json_annotation/json_annotation.dart';

part 'file_info.g.dart';

@JsonSerializable()
class FileInfo {
  final String path;
  final String name;
  final int size;
  @JsonKey(name: 'size_formatted')
  final String sizeFormatted;
  final String type;
  final String extension;
  @Json<PERSON>ey(name: 'modified_time')
  final double modifiedTime;
  @JsonKey(name: 'modified_time_formatted')
  final String modifiedTimeFormatted;
  @Json<PERSON>ey(name: 'a_part')
  final String aPart;
  @JsonKey(name: 'b_part')
  final String bPart;
  final String caption;
  final String hash;

  FileInfo({
    required this.path,
    required this.name,
    required this.size,
    required this.sizeFormatted,
    required this.type,
    required this.extension,
    required this.modifiedTime,
    required this.modifiedTimeFormatted,
    required this.aPart,
    required this.bPart,
    required this.caption,
    required this.hash,
  });

  factory FileInfo.fromJson(Map<String, dynamic> json) => _$FileInfoFromJson(json);
  Map<String, dynamic> toJson() => _$FileInfoToJson(this);
}

@JsonSerializable()
class FolderStats {
  @JsonKey(name: 'total_files')
  final int totalFiles;
  @JsonKey(name: 'video_count')
  final int videoCount;
  @JsonKey(name: 'audio_count')
  final int audioCount;
  @JsonKey(name: 'image_count')
  final int? imageCount;
  @JsonKey(name: 'document_count')
  final int? documentCount;
  @JsonKey(name: 'archive_count')
  final int? archiveCount;
  @JsonKey(name: 'other_count')
  final int? otherCount;
  @JsonKey(name: 'total_size')
  final int totalSize;
  @JsonKey(name: 'total_size_formatted')
  final String totalSizeFormatted;
  @JsonKey(name: 'folder_path')
  final String folderPath;
  @JsonKey(name: 'total_files_in_folder')
  final int? totalFilesInFolder;
  @JsonKey(name: 'supported_extensions')
  final Map<String, dynamic>? supportedExtensions;

  FolderStats({
    required this.totalFiles,
    required this.videoCount,
    required this.audioCount,
    this.imageCount,
    this.documentCount,
    this.archiveCount,
    this.otherCount,
    required this.totalSize,
    required this.totalSizeFormatted,
    required this.folderPath,
    this.totalFilesInFolder,
    this.supportedExtensions,
  });

  factory FolderStats.fromJson(Map<String, dynamic> json) => _$FolderStatsFromJson(json);
  Map<String, dynamic> toJson() => _$FolderStatsToJson(this);
} 