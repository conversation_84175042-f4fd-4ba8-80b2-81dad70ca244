('E:\\github\\local_upload\\build\\launcher\\PYZ-00.pyz',
 [('__future__', 'C:\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'C:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'C:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'C:\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'C:\\Python312\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('idna',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'C:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'C:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'C:\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'C:\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors', 'C:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('socks',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('ssl', 'C:\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'C:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python312\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
