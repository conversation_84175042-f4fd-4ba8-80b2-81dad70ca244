^E:\GITHUB\LOCAL_UPLOAD\FRONTEND\BUILD\WINDOWS\X64\CMAKEFILES\58460D0C42C0A29CB04E1A80D5DD5BDA\GENERATE.STAMP.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/github/local_upload/frontend/windows -BE:/github/local_upload/frontend/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/github/local_upload/frontend/build/windows/x64/telegram_uploader.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
