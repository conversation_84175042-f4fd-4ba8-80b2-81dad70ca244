import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/task_provider.dart';
import '../models/task.dart';

class StatsPanel extends StatelessWidget {
  const StatsPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AppProvider, TaskProvider>(
      builder: (context, appProvider, taskProvider, child) {
        return Card(
          color: Colors.green.shade50,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '实时统计',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 12),
                
                // 上传统计
                if (appProvider.uploadStats != null)
                  _buildUploadStats(appProvider.uploadStats!),
                
                const SizedBox(height: 12),
                
                // 任务统计
                _buildTaskStats(taskProvider),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildUploadStats(UploadStats stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.speed, size: 20, color: Colors.blue),
            const SizedBox(width: 8),
            Text(
              '上传速度: ${stats.speedFormatted}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.cloud_upload, size: 20, color: Colors.green),
            const SizedBox(width: 8),
            Text(
              '活跃上传: ${stats.activeUploads}',
              style: TextStyle(color: Colors.green),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.data_usage, size: 20, color: Colors.orange),
            const SizedBox(width: 8),
            Text(
              '已上传: ${_formatBytes(stats.totalUploaded)}',
              style: TextStyle(color: Colors.orange),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildTaskStats(TaskProvider taskProvider) {
    final stats = taskProvider.getTaskStats();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.list, size: 20, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Text(
              '任务统计',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // 任务统计网格
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                '总任务',
                stats['total']!,
                Icons.assignment,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatItem(
                '运行中',
                stats['running']!,
                Icons.play_arrow,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                '已完成',
                stats['completed']!,
                Icons.check_circle,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatItem(
                '失败',
                stats['failed']!,
                Icons.error,
                Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildStatItem(String label, int value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            '$value',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 16,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
  
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
} 