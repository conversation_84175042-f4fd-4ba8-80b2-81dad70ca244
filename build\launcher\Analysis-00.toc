(['E:\\github\\local_upload\\launcher.py'],
 ['E:\\github\\local_upload'],
 ['requests'],
 ['E:\\github\\local_upload\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'E:\\github\\local_upload\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 {},
 [],
 [('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_asyncio.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_asyncio.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_bz2.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_bz2.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_ctypes.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_ctypes.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_decimal.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_decimal.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_hashlib.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_hashlib.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_lzma.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_lzma.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_multiprocessing.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_multiprocessing.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_overlapped.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_overlapped.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_queue.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_queue.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_socket.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_socket.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_sqlite3.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_sqlite3.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_ssl.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_ssl.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_uuid.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_uuid.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\_wmi.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_wmi.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\base_library.zip',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\base_library.zip',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\LICENSE.rst',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\entry_points.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\pyexpat.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\pyexpat.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\python312.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\python312.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\select.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\select.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\tgcrypto.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\tgcrypto.cp312-win_amd64.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\unicodedata.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\unicodedata.pyd',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\telegram_backend.exe',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\telegram_backend.exe',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\app.so',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\app.so',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.bin',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.bin',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\FontManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\FontManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NOTICES.Z',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NOTICES.Z',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NativeAssetsManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NativeAssetsManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\assets\\icons\\app_icon.ico',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\assets\\icons\\app_icon.ico',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\fonts\\MaterialIcons-Regular.otf',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\fonts\\MaterialIcons-Regular.otf',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\shaders\\ink_sparkle.frag',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\shaders\\ink_sparkle.frag',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\icudtl.dat',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\icudtl.dat',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\telegram_uploader.exe',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\telegram_uploader.exe',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'DATA')],
 '3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('launcher', 'E:\\github\\local_upload\\launcher.py', 'PYSOURCE')],
 [('inspect', 'C:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('importlib', 'C:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'C:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'C:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'C:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('copy', 'C:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('struct', 'C:\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('string', 'C:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'C:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'C:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'C:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'C:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('datetime', 'C:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'C:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'C:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.util', 'C:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('email', 'C:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'C:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('token', 'C:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'C:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('requests',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('urllib.request', 'C:\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python312\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('http.client', 'C:\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookies', 'C:\\Python312\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('json', 'C:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('requests.models',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('hmac', 'C:\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('queue', 'C:\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('urllib3.util.response',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('urllib3.filepost',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('ssl', 'C:\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('charset_normalizer',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('signal', 'C:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('threading', 'C:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python312\\Lib\\subprocess.py', 'PYMODULE')],
 [('backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_asyncio.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_asyncio.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_bz2.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_bz2.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_ctypes.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_ctypes.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_decimal.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_decimal.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_hashlib.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_hashlib.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_lzma.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_lzma.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_multiprocessing.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_multiprocessing.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_overlapped.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_overlapped.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_queue.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_queue.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_socket.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_socket.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_sqlite3.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_sqlite3.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_ssl.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_ssl.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_uuid.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_uuid.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_wmi.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_wmi.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\pyexpat.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\pyexpat.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\python312.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\python312.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\select.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\select.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\tgcrypto.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\tgcrypto.cp312-win_amd64.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\unicodedata.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\unicodedata.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\telegram_backend.exe',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\telegram_backend.exe',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\telegram_uploader.exe',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\telegram_uploader.exe',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'BINARY'),
  ('python312.dll', 'C:\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'BINARY'),
  ('libffi-8.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('flutter_windows.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\WINDOWS\\system32\\MSVCP140.dll', 'BINARY'),
  ('window_manager_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'BINARY'),
  ('system_tray_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'BINARY'),
  ('screen_retriever_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'BINARY')],
 [],
 [],
 [('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\base_library.zip',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\base_library.zip',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\LICENSE.rst',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\entry_points.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\app.so',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\app.so',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.bin',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.bin',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\FontManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\FontManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NOTICES.Z',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NOTICES.Z',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NativeAssetsManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NativeAssetsManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\assets\\icons\\app_icon.ico',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\assets\\icons\\app_icon.ico',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\fonts\\MaterialIcons-Regular.otf',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\fonts\\MaterialIcons-Regular.otf',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\shaders\\ink_sparkle.frag',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\shaders\\ink_sparkle.frag',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\icudtl.dat',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\icudtl.dat',
   'DATA'),
  ('base_library.zip',
   'E:\\github\\local_upload\\build\\launcher\\base_library.zip',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA')])
