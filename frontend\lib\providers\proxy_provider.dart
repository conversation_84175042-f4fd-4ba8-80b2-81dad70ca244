import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../models/proxy_config.dart';

class ProxyProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  // 状态
  bool _isLoading = false;
  String? _error;
  
  // 数据
  List<ProxyConfig> _proxyConfigs = [];
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ProxyConfig> get proxyConfigs => _proxyConfigs;
  
  // 获取已启用的代理
  List<ProxyConfig> get enabledProxies => 
      _proxyConfigs.where((proxy) => proxy.enabled).toList();
  
  // 获取默认代理
  ProxyConfig? get defaultProxy => 
      _proxyConfigs.where((proxy) => proxy.isDefault && proxy.enabled).isNotEmpty
          ? _proxyConfigs.firstWhere((proxy) => proxy.isDefault && proxy.enabled)
          : null;
  
  // 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // 设置错误
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  
  // 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  // 加载代理配置列表
  Future<void> loadProxyConfigs() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.getProxyConfigs();
      
      if (response.success) {
        _proxyConfigs = response.data ?? [];
        notifyListeners();
      } else {
        _setError(response.error);
      }
    } catch (e) {
      _setError('加载代理配置失败: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // 添加代理配置
  Future<bool> addProxyConfig({
    required String name,
    required String proxyType,
    required String hostname,
    required int port,
    String? username,
    String? password,
    bool enabled = false,
    bool isDefault = false,
  }) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.addProxyConfig(
        name: name,
        proxyType: proxyType,
        hostname: hostname,
        port: port,
        username: username,
        password: password,
        enabled: enabled,
        isDefault: isDefault,
      );
      
      if (response.success) {
        // 重新加载代理配置列表
        await loadProxyConfigs();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('添加代理配置失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // 更新代理配置
  Future<bool> updateProxyConfig(int proxyId, Map<String, dynamic> data) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.updateProxyConfig(proxyId, data);
      
      if (response.success) {
        // 重新加载代理配置列表
        await loadProxyConfigs();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('更新代理配置失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // 删除代理配置
  Future<bool> deleteProxyConfig(int proxyId) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await _apiService.deleteProxyConfig(proxyId);
      
      if (response.success) {
        // 重新加载代理配置列表
        await loadProxyConfigs();
        return true;
      } else {
        _setError(response.error);
        return false;
      }
    } catch (e) {
      _setError('删除代理配置失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // 测试代理配置
  Future<ProxyTestResult?> testProxyConfig(int proxyId) async {
    try {
      final response = await _apiService.testProxyConfig(proxyId);
      
      if (response.success) {
        return response.data;
      } else {
        _setError(response.error);
        return null;
      }
    } catch (e) {
      _setError('测试代理配置失败: $e');
      return null;
    }
  }
  
  // 启用/禁用代理
  Future<bool> toggleProxyEnabled(int proxyId, bool enabled) async {
    return await updateProxyConfig(proxyId, {'enabled': enabled});
  }
  
  // 设置默认代理
  Future<bool> setDefaultProxy(int proxyId) async {
    return await updateProxyConfig(proxyId, {
      'is_default': true,
      'enabled': true,
    });
  }
} 